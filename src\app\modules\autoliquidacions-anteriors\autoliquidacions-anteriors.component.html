<div class="app-autoliquidacions-anterios mb-5 mt-4">
  <div class="mb-4" *ngIf="autoExpedientMap.size">
    <se-panel
      [title]="
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.TITLE_PANEL' | translate
      "
    >
      <mf-autoliquidacions-presentades-info-pagament></mf-autoliquidacions-presentades-info-pagament>
      <p
        [innerHTML]="
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.SUBTITLE1' | translate
        "
      ></p>
      <p
        [innerHTML]="
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.SUBTITLE2' | translate
        "
      ></p>
      <p
        [innerHTML]="
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.SUBTITLE3' | translate
        "
      ></p>
      <div
        class="panel-auto mt-4"
        *ngFor="let autos of autoExpedientMap | keyvalue"
      >
        <mf-autoliquidacions-presentades-resultat
          [titleLabelPanel]="
            ('SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.EXPEDIENT' | translate) +
            autos.key
          "
          [model]="modelFraccionament"
          [idAutoliquidacions]="autos.value"
          [showAlerts]="false"
          [showInfoPagament]="false"
          [showNovaAutoliquidacioButton]="false"
          [panelTheme]="'secondary'"
          (payButtonEvent)="goToPagamentHandler($event)"
          (redirectSessionTreballEvent)="redirectSessionTreballHandler()"
        >
        </mf-autoliquidacions-presentades-resultat>
      </div>
    </se-panel>
  </div>
  <se-panel
    class="panel-0-padding"
    [title]="
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.TITLE_PANEL_ONGOING' | translate
    "
    *ngIf="this.goingRows.length"
  >
    <p class="p-4">
      {{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.SUBTITLE_ONGOING' | translate
      }}
    </p>
    <se-table [columns]="goingColumns" [data]="goingRows"></se-table>
  </se-panel>

  <div class="d-flex justify-content-end align-items-end mt-4 mb-4">
    <se-button type="submit" (onClick)="newFormActionHandler()">
      {{ 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.NEWFORM' | translate }}
    </se-button>
  </div>
</div>

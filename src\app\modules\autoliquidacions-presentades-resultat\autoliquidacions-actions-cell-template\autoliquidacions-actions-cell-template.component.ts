/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MenuItem } from 'primeng/api';
import {
  ButtonCellConfig,
  CellComponent,
  CellConfig,
  CellEventService,
  CellEventTypes,
  Column,
  FlattenedCell,
  FlattenedRow,
  SeButton,
  SeButtonThemeEnum,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-autoliquidaciones-actions-cell',
  template: `
    <div class="button-row-container d-flex flex-wrap gap-2">
      <se-button
        class="button-align"
        [btnTheme]="button.buttonConfig.btnTheme ?? 'trueOnlyText'"
        [disabled]="!!button.buttonConfig.disabled"
        [icon]="button.buttonConfig.icon ?? 'matEuroSymbolSharp'"
        [iconSize]="button.buttonConfig.iconSize ?? '20px'"
        [iconPosition]="button.buttonConfig.iconPosition ?? 'left'"
        [type]="button.buttonConfig.type ?? 'button'"
        [ariaLabel]="
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.PANEL_ACTIONS.PAGAR'
            | translate
        "
        [title]="button.buttonConfig.title ?? '' | translate"
        (onClick)="onActionButton()"
      >
      </se-button>
      <se-button-dropdown
        [buttonOptions]="buttonOptions"
        [items]="actions"
      ></se-button-dropdown>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AutoliquidacionsActionsCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  get button(): ButtonCellConfig {
    return this.cellConfig.buttonCell!;
  }

  get actions(): MenuItem[] {
    return this.cellConfig['actions'];
  }

  get buttonOptions(): SeButton {
    return {
      icon: 'matMoreVertOutline',
      iconSize: '20px',
      btnTheme: SeButtonThemeEnum.TRUE_ONLY_TEXT,
      ariaLabel:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.PANEL_ACTIONS.ALTRES_ACCIONS',
      title: this.translateService.instant(
        'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.ACTIONS',
      ),
    };
  }

  constructor(
    private readonly cellEventService: CellEventService,
    private translateService: TranslateService,
  ) {}

  async onActionButton(): Promise<void> {
    if (!this.button.buttonCallback)
      return this.throwEvent(
        CellEventTypes.ACTION_BUTTON_ROW,
        'buttonCellComponent',
      );
    const { data } = await this.button.buttonCallback(
      this.row,
      this.cell,
      this.column,
    );

    if (data) {
      this.throwEvent(CellEventTypes.ACTION_BUTTON_ROW, 'buttonCellComponent', {
        newData: data,
        rowId: this.row.id,
      });
    }
  }

  private throwEvent(
    type: CellEventTypes,
    cellName: string,
    data?: any,
    apply = false,
  ): void {
    this.cellEventService.emitEvent({
      type,
      cellName,
      cell: this.cell,
      data: data ? { ...data, apply } : { apply },
    });
  }
}

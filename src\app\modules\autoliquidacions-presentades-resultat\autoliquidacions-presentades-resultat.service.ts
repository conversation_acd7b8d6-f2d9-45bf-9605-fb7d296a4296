import { EventEmitter, Injectable } from '@angular/core';
import { SelfAssessmentEstat } from '@core/models';
import { TranslateService } from '@ngx-translate/core';
import {
  ButtonCallbackReturn,
  Cell,
  CellConfig,
  Column,
  FlattenedCell,
  FlattenedRow,
  Nullable,
  Row,
  SeDocumentsService,
  SeLoginService,
} from 'se-ui-components-mf-lib';
import { TableAutoliquidacioData } from './autoliquidacions-presentades-resultat.model';

import { Autoliquidacio } from '@core/models/autoliquidacio.model';
import { AutoliquidacionsService } from '@core/services/autoliquidacions.service';
import { MenuItem } from 'primeng/api';
import { take } from 'rxjs';
import { AutoliquidacionsActionsCellComponent } from './autoliquidacions-actions-cell-template/autoliquidacions-actions-cell-template.component';
import { AutoPresResultatEndpointService } from './autoliquidacions-presentades-resultat-endpoints.service';
import { PagantCellComponent } from './pagant-cell-template/pagant-cell-template.component';

@Injectable({
  providedIn: 'root',
})
export class AutoPresResultatService {
  seuUrl: string = '';
  model: string = '';
  payButtonEvent: Nullable<EventEmitter<string[]>>;
  constructor(
    private readonly translateService: TranslateService,
    private readonly autoliquidacionsPresentadesResultatEndpointService: AutoPresResultatEndpointService,
    private readonly seDocumentsService: SeDocumentsService,
    private readonly autoliquidacionsService: AutoliquidacionsService,
    private readonly seLoginService: SeLoginService,
  ) {}

  initData(model: string, payButtonEvent: EventEmitter<string[]>): void {
    this.model = model;
    this.payButtonEvent = payButtonEvent;
    this.setSeuUrl();
  }

  getTableColums(showActions: boolean): Column[] {
    const columns: Column[] = [
      {
        header: this.translateService.instant(
          `SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.${'justificant'.toUpperCase()}`,
        ),
        size: 15,
        key: 'justificant',
        resizable: false,
        cellConfig: {
          translateNoAttr: true,
        },
      },
      {
        header: this.translateService.instant(
          `SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.${'taxpayer'.toUpperCase()}`,
        ),
        size: 25,
        key: 'taxpayer',
        resizable: false,
        cellConfig: {
          translateNoAttr: true,
        },
      },
      {
        header: this.translateService.instant(
          `SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.${'date'.toUpperCase()}`,
        ),
        size: 15,
        key: 'date',
        resizable: false,
        cellComponentName: 'dateCellComponent',
      },
      {
        header: this.translateService.instant(
          `SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.${'total'.toUpperCase()}`,
        ),
        size: 15,
        key: 'total',
        resizable: false,
        cellComponentName: 'currencyCellComponent',
        cellConfig: {
          align: 'right',
          translateNoAttr: true,
        },
      },
      {
        header: this.translateService.instant(
          `SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.${'state'.toUpperCase()}`,
        ),
        size: 15,
        key: 'state',
        resizable: false,
      },
    ];
    if (showActions) {
      columns.push({
        header: `SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.ACTIONS`,
        size: 15,
        key: 'actions',
      });
    }
    return columns;
  }

  getTableData(autoliquidacions: Autoliquidacio[]): Row[] {
    const rows: Row[] = [];
    for (const auto of autoliquidacions) {
      let data: TableAutoliquidacioData = new TableAutoliquidacioData(auto);
      data = {
        ...data,
        state: {
          value: this.getStat(auto.estat),
        },
      };

      rows.push({
        // Desabilito seleccionar la autoliqudiacio para pagar si esta finalizada
        rowConfig: {
          isDisabled: this.autoliquidacionsService.checkStatDraftFinalize(auto),
        },
        data: {
          ...Object.fromEntries(
            Object.entries(data).map(([key, { value }]) => {
              if (key === 'state') {
                const tooltip = this.getErrorPament(data, value);

                let cellComponentName = '';
                let cellComponent = null;
                if (value.includes(SelfAssessmentEstat.PAGANT)) {
                  cellComponent = PagantCellComponent;
                } else if (tooltip.length) {
                  cellComponentName = 'separatedTooltipCellComponent';
                }

                return [
                  key,
                  {
                    value,
                    cellComponent,
                    cellComponentName,
                    cellConfig: {
                      tooltip: tooltip.length,
                      tooltipText: tooltip,
                      ngStyle: {
                        color: this.getColorForState(data, value),
                      },
                    },
                  },
                ];
              }
              return [key, { value }];
            }),
          ),
          ...this.getPayButton(auto.estat, data),
        },
      });
    }
    return rows;
  }

  getPayButton = (
    state: string,
    data: TableAutoliquidacioData,
  ): { [key: string]: Cell } => {
    if (
      state === SelfAssessmentEstat.PRESENTAT ||
      state === SelfAssessmentEstat.PENDENT_PAGAMENT ||
      state === SelfAssessmentEstat.PAGAMENT_ERROR
    ) {
      return {
        actions: {
          value: '',
          cellComponent: AutoliquidacionsActionsCellComponent,
          cellConfig: this.handleCellConfig('actions', data),
        },
      };
    }

    return { actions: { value: '' } };
  };

  private handleCellConfig(
    key: string,
    data: TableAutoliquidacioData,
  ): CellConfig {
    if (key.includes('actions')) {
      const actions = this.getActions();
      return {
        align: 'right',
        buttonCell: {
          buttonConfig: {
            title: 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.PAGAR',
            disabled: Number(data.total.value) <= 0,
          },
          buttonCallback: (
            _row: FlattenedRow,
            cell: FlattenedCell,
          ): Promise<ButtonCallbackReturn> => {
            return new Promise(() => {
              this.payButtonEvent?.emit([
                cell.rowData['idAutoliquidacio'].value,
              ]);
            });
          },
        },
        actions: actions?.map((action) => ({
          ...action,
          data,
          disabled: action['checkDisabled']
            ? Number(data.total.value) <= 0
            : false,
        })),
      };
    }
    return {};
  }

  getErrorPament(
    data: TableAutoliquidacioData,
    value: SelfAssessmentEstat,
  ): string {
    let tooltip = '';
    if (value.includes(SelfAssessmentEstat.PAGAMENT_ERROR)) {
      if (data.errors.value.length) {
        const error = data.errors.value[0];
        if (
          !this.translateService
            .instant(`UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.${error.code}`)
            .includes('UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS')
        ) {
          tooltip += `${this.translateService.instant(
            `UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.${error.code}`,
          )}`;
        }
      }
    }
    return tooltip;
  }

  getColorForState(
    data: TableAutoliquidacioData,
    value: SelfAssessmentEstat,
  ): string {
    let color = '';
    if (value.includes(SelfAssessmentEstat.PRESENTAT)) {
      color =
        Number(data['total'].value) > 0
          ? 'var(--color-orange-300)'
          : 'var(--color-green-300)';
    } else if (
      value.includes(SelfAssessmentEstat.PAGAMENT_ERROR) ||
      value.includes(SelfAssessmentEstat.NOTIFICACIO_ERROR)
    ) {
      color = 'var(--color-red-400)';
    } else if (value.includes(SelfAssessmentEstat.PAGAT)) {
      color = 'var(--color-green-300)';
    }
    return color;
  }

  getStat(estatAuto: string): string {
    return estatAuto ? `COMMONS.SELF_ASSESSMENT_ESTATS.${estatAuto}` : '';
  }

  BASE_TRANSLATION = 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES';

  getActions(): MenuItem[] {
    return [
      {
        checkDisabled: true,
        label: this.translateService.instant(
          `${this.BASE_TRANSLATION}.TABLE_ACTIONS.PAYMENT_RECEIPT`,
        ),
        command: (event): void => {
          const data: TableAutoliquidacioData = (event.item as MenuItem)[
            'data'
          ];
          this.downloadJustificantPdf(data);
        },
      },
      {
        checkDisabled: true,
        label: this.translateService.instant(
          `${this.BASE_TRANSLATION}.TABLE_ACTIONS.PAYMENT_POSTPONEMENT`,
        ),
        command: (event): void => {
          if (event.item) {
            const data: TableAutoliquidacioData = (event.item as MenuItem)[
              'data'
            ];
            if (data?.justificant?.value && data?.nif?.value && this.seuUrl) {
              window.open(
                this.getUrlPaymentPostponement(
                  data.justificant.value,
                  data.nif.value,
                ),
              );
            }
          }
        },
      },
      {
        label: this.translateService.instant(
          `${this.BASE_TRANSLATION}.TABLE_ACTIONS.VIEW_SELFASSESMENT`,
        ),
        command: (event): void => {
          const data: TableAutoliquidacioData = (event.item as MenuItem)[
            'data'
          ];
          this.downloadJustificantPdf(data);
        },
      },
    ];
  }

  getUrlPaymentPostponement(refJustificant: string, nif: string): string {
    return `${this.seuUrl}/${this.translateService.currentLang}/OficinaVirtual/Pagines/TRAjornaments.aspx?refJustificant=${refJustificant}&nif=${nif}&model=${this.model}`;
  }

  setSeuUrl(): void {
    this.seLoginService.getSeuUrlByEnv().then((response) => {
      if (response?.content) {
        this.seuUrl = response.content;
      }
    });
  }

  downloadJustificantPdf(data: TableAutoliquidacioData): void {
    if (data?.justificant?.value && data?.idMfpt?.value) {
      this.autoliquidacionsPresentadesResultatEndpointService
        .downloadJustificantPdf(data?.idMfpt?.value)
        .pipe(take(1))
        .subscribe((result) => {
          if (result?.content?.base64File) {
            this.seDocumentsService.openFile(
              result.content.base64File,
              'application/pdf',
              `${data.justificant.value}.pdf`,
            );
          }
        });
    }
  }

  hasError(estat: SelfAssessmentEstat): boolean {
    return (
      estat === SelfAssessmentEstat.ERROR ||
      estat === SelfAssessmentEstat.PRESENTACIO_ERROR ||
      estat === SelfAssessmentEstat.PAGAMENT_ERROR ||
      estat === SelfAssessmentEstat.NOTIFICACIO_ERROR
    );
  }
}

import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AutoliquidacionsAnteriorsComponent } from './autoliquidacions-anteriors.component';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonModule,
  SePanelModule,
  SeTableModule,
} from 'se-ui-components-mf-lib';
import { AutoliquidacionsAnteriorsOldComponent } from './old/autoliquidacions-anteriors-old.component';

@NgModule({
  declarations: [
    AutoliquidacionsAnteriorsComponent,
    AutoliquidacionsAnteriorsOldComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    SePanelModule,
    SeTableModule,
    SeButtonModule,
  ],
  exports: [AutoliquidacionsAnteriorsComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AutoliquidacionsAnteriorsModule {}

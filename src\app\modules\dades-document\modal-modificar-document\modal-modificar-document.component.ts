import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { FormGroup, ValidatorFn } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
//LIBRARY
import { firstValueFrom, Subject, takeUntil } from 'rxjs';
import {
  DateUtilsService,
  Nullable,
  SeModal,
  SeModalOutputEvents,
} from 'se-ui-components-mf-lib';
import { DadesDocumentEndpointsService } from '../dades-document-endpoints.service';
import {
  FormDocumentValues,
  RequestFormDocument,
} from '../models/dades-document.model';
import { DraftTributs } from '../models/draft-tributs.model';
import { TAX_MODEL } from '@core/models';

@Component({
  selector: 'app-modal-modificar-document',
  templateUrl: './modal-modificar-document.component.html',
})
export class ModalModificarDocumentComponent implements OnInit, OnDestroy {
  data!: SeModal;
  componentForm!: FormGroup;
  foundSessions: boolean = false;

  @Input() impost: Nullable<TAX_MODEL>;
  @Input() documentDateValidators: ValidatorFn[] = [];
  @Input() codiTipusOperacio: Nullable<string>;
  @Input() formDocumentValues!: FormDocumentValues;
  @Input() tooltipPrivatReference: Nullable<string>;

  @Output() modalOutput: EventEmitter<FormGroup> =
    new EventEmitter<FormGroup>();
  @Output() updateFormulari: EventEmitter<RequestFormDocument> =
    new EventEmitter<RequestFormDocument>();

  private readonly unsubscribe: Subject<void> = new Subject();
  constructor(
    public activeModal: NgbActiveModal,
    private dateUtilsService: DateUtilsService,
    private dadesDocumentEndpointsService: DadesDocumentEndpointsService,
  ) {}

  ngOnInit(): void {
    this.getData();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  getFormData = (form: FormGroup): void => {
    this.componentForm = form;
    this.componentForm.valueChanges
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.changeCloseModal(false);
      });
  };

  private getData = (): void => {
    this.data = {
      closable: true,
      closableLabel: 'SE_TRIBUTS_MF.DADES_DOCUMENT.BUTTONS.SAVE',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_TRIBUTS_MF.DADES_DOCUMENT.BUTTONS.CANCEL',
      title: 'SE_TRIBUTS_MF.DADES_DOCUMENT.MODAL_EDIT.TITLE',
      subtitle: 'SE_TRIBUTS_MF.DADES_DOCUMENT.MODAL_EDIT.SUBTITLE',
    };
  };

  async saveData(output: string): Promise<void> {
    this.foundSessions = false;
    if (output === SeModalOutputEvents.MAIN_ACTION) {
      //SAVE DATA
      this.componentForm.markAsDirty();
      this.componentForm.markAllAsTouched();

      if (this.componentForm.invalid) return;

      if (this.codiTipusOperacio) {
        // obetenr formularios con los mismos datos de documento y operacion
        const formValues = this.componentForm.getRawValue();
        formValues.documentDate = this.dateUtilsService.formatDate(
          formValues.documentDate,
          'yyyy-MM-dd',
        );
        const request = new RequestFormDocument(formValues);

        const formularis = await this.getFilteredFormularis(
          request,
          this.codiTipusOperacio,
        );
        if (formularis.length) {
          //mostrar mensaje error y data.closable.disabled = true
          this.changeCloseModal(true);
        } else {
          // update formulari y abrir modal form-operacio
          this.closeModal();
          this.updateFormulari.emit(request);
        }
      } else {
        this.closeModal();
        this.modalOutput.emit(this.componentForm);
      }
    }
  }

  async getFilteredFormularis(
    request: RequestFormDocument,
    codiTipusOperacio: string,
  ): Promise<DraftTributs[]> {
    let formularis: DraftTributs[] = [];
    if (this.impost) {
      const result = await firstValueFrom(
        this.dadesDocumentEndpointsService.getWorkingSession(
          request,
          this.impost,
        ),
      );
      if (result?.content && this.codiTipusOperacio) {
        formularis = result.content.filter(
          (form) => form.operacions[0]?.codiTipusOperacio === codiTipusOperacio,
        );
      }
    }
    return formularis;
  }

  closeModal = (): void => {
    this.activeModal.close();
  };

  changeCloseModal(value: boolean): void {
    this.foundSessions = value;
    this.data.closableDisabled = value;
  }
}

import { Injectable } from '@angular/core';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { map, Observable, Subject, take } from 'rxjs';
import { SeModalOutputEvents, SeModalService } from 'se-ui-components-mf-lib';
import { AutoliquidacioComplementariaEndpointsService } from './autoliquidacio-complementaria-endpoints.service';
import {
  RequestComplementaryFormulari,
  ResponseCheckComplementari,
} from './models/autoliquidacio-complementaria.model';

@Injectable({
  providedIn: 'root',
})
export class AutoliquidacioComplementariaService {
  constructor(
    private readonly autoliquidacioComplementariaEndpointsService: AutoliquidacioComplementariaEndpointsService,
    private readonly seModalService: SeModalService,
  ) {}
  private readonly redirectSubject = new Subject<string>();
  public readonly redirect$ = this.redirectSubject.asObservable();

  set redirect(idTramit: string) {
    this.redirectSubject.next(idTramit);
  }

  checkAutoComp(
    request: RequestComplementaryFormulari,
  ): Observable<ResponseCheckComplementari | undefined> {
    return this.autoliquidacioComplementariaEndpointsService
      .checkAutoComp(request)
      .pipe(
        take(1),
        map((result) => {
          const recuperada =
            result.content?.existeixComplementaria &&
            !result.content.idFormulari;
          recuperada && this.openModalAutoCompRecuperada(request);
          return result.content;
        }),
      );
  }

  openModalAutoCompRecuperada(request: RequestComplementaryFormulari): void {
    const modalRef: NgbModalRef = this.seModalService.openModal({
      title:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.MODAL_AUTOLIQUIDACIO_RECUPERADA.TITLE',
      subtitle:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.MODAL_AUTOLIQUIDACIO_RECUPERADA.SUBTITLE',
      closable: true,
      closableLabel: 'SE_TRIBUTS_MF.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_TRIBUTS_MF.BUTTONS.CANCEL',
    });

    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(take(1))
      .subscribe(() => {
        modalRef.close();
        this.redirect = '';
      });

    modalRef.componentInstance.modalOutputEvent
      .pipe(take(1))
      .subscribe((modalOutput: SeModalOutputEvents) => {
        if (modalOutput === SeModalOutputEvents.MAIN_ACTION) {
          this.copiDataAutoliquidacio(request);
        }
        modalRef.close();
      });
  }

  copiDataAutoliquidacio(request: RequestComplementaryFormulari): void {
    this.autoliquidacioComplementariaEndpointsService
      .crearSesioncomplementaria(request)
      .subscribe((result) => {
        if (result?.content) {
          this.redirect = result.content.idFormulari;
        }
      });
  }
}

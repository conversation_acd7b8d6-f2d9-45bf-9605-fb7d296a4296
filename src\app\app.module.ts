import { registerLocaleData } from '@angular/common';
import {
  HTTP_INTERCEPTORS,
  HttpBackend,
  HttpClientModule,
} from '@angular/common/http';
import localeCa from '@angular/common/locales/ca';
import {
  APP_INITIALIZER,
  ApplicationRef,
  DEFAULT_CURRENCY_CODE,
  DoBootstrap,
  Injector,
  LOCALE_ID,
  NgModule,
  isDevMode,
} from '@angular/core';
import { createCustomElement } from '@angular/elements';
import { BrowserModule } from '@angular/platform-browser';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { CookieService } from 'ngx-cookie-service';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';

import { PrimeNGConfig } from 'primeng/api';
import { lastValueFrom, take } from 'rxjs';
import {
  SeHttpInterceptorService,
  SeLoginService,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { AutoliquidacioComplementariaComponent } from './modules/autoliquidacio-complementaria/autoliquidacio-complementaria.component';
import { AutoliquidacionsAnteriorsComponent } from './modules/autoliquidacions-anteriors/autoliquidacions-anteriors.component';
import { AutoliquidacionsAnteriorsModule } from './modules/autoliquidacions-anteriors/autoliquidacions-anteriors.module';
import { AutoliquidacionsAnteriorsOldComponent } from './modules/autoliquidacions-anteriors/old/autoliquidacions-anteriors-old.component';
import { AutoliquidacionsComplementariaPerduaFiscalComponent } from './modules/autoliquidacions-complementaria-perdua-fiscal/autoliquidacions-complementaria-perdua-fiscal.component';
import { AutoliquidacionsComplementariaPerduaFiscalModule } from './modules/autoliquidacions-complementaria-perdua-fiscal/autoliquidacions-complementaria-perdua-fiscal.module';
import { AutoliquidacionsForaTerminiComponent } from './modules/autoliquidacions-fora-termini/autoliquidacions-fora-termini.component';
import { AutoliquidacionsForaTerminiModule } from './modules/autoliquidacions-fora-termini/autoliquidacions-fora-termini.module';
import { AutoPresResultatComponent } from './modules/autoliquidacions-presentades-resultat/autoliquidacions-presentades-resultat.component';
import { AutoPresResultatModule } from './modules/autoliquidacions-presentades-resultat/autoliquidacions-presentades-resultat.module';
import { InformacioPagamentComponent } from './modules/autoliquidacions-presentades-resultat/informacio-pagament/informacio-pagament.component';
import { CalculTreeModule } from './modules/calcul-tree/calcul-tree.module';
import { CalculResumComponent } from './modules/calcul-tree/components/calcul-resum/calcul-resum.component';
import { CalculTreeViewComponent } from './modules/calcul-tree/components/calcul-tree-view/calcul-tree-view.component';
import { CalculationsComponent } from './modules/calculations/calculations.component';
import { CalculationsModule } from './modules/calculations/calculations.module';
import { SummaryComponent } from './modules/calculations/summary/summary.component';
import { SummaryModule } from './modules/calculations/summary/summary.module';
import { SurchargesComponent } from './modules/calculations/surcharges/surcharges.component';
import { SurchargesModule } from './modules/calculations/surcharges/surcharges.module';
import {
  SeAutomaticComplementaryComponent,
  SeComplementaryModule,
} from './modules/complementary';
import { SeManualComplementaryComponent } from './modules/complementary/manual-complementary/manual-complementary.component';
import { ConfirmRecoverDraftComponent } from './modules/confirm-recover-draft/confirm-recover-draft.component';
import { ConfirmRecoverDraftModule } from './modules/confirm-recover-draft/confirm-recover-draft.module';
import { DadesDocumentComponent } from './modules/dades-document/dades-document.component';
import { DadesDocumentModule } from './modules/dades-document/dades-document.module';
import { DataFiTerminiComponent } from './modules/data-fi-termini/data-fi-termini.component';
import { DataFiTerminiComponentModule } from './modules/data-fi-termini/data-fi-termini.module';
import { ModifySaveButtonsComponent } from './modules/modify-save-buttons/modify-save-buttons.component';
import { ModifySaveButtonsModule } from './modules/modify-save-buttons/modify-save-buttons.module';
import { AutoPendentsTramitarComponent } from './modules/resum/autoliquidacions-pendents-tramitar.component';
import { AutoPendentsTramitarModule } from './modules/resum/autoliquidacions-pendents-tramitar.module';
import { SelfAssessmentResultComponent } from './modules/self-assessment-result/self-assessment-result.component';
import { SelfAssessmentResultModule } from './modules/self-assessment-result/self-assessment-result.module';
import { TaxFormGroupComponent, TaxFormsModule } from './modules/tax-forms';
import { TaxpayerComponent, TaxpayerModule } from './modules/taxpayer';
import { WorkingSessionsComponent } from './modules/working-sessions/working-sessions.component';
import { WorkingSessionsModule } from './modules/working-sessions/working-sessions.module';
import { YearPeriodComponent } from './modules/year-period/year-period.component';
import { YearPeriodModule } from './modules/year-period/year-period.module';

function HttpLoaderFactory(httpBackend: HttpBackend) {
  return new MultiTranslateHttpLoader(httpBackend, [
    { prefix: `${environment.baseUrlMf}/assets/i18n/`, suffix: '.json' },
    { prefix: `${environment.baseUrCommons}/i18n/`, suffix: '.json' },
  ]);
}

export function appInitializerFactory(
  translate: TranslateService,
  primeNGConfig: PrimeNGConfig,
): () => Promise<unknown> {
  return () => {
    translate.addLangs(['ca', 'es']);
    translate.setDefaultLang('ca');

    const currentLang = window.location.href.includes('/es/') ? 'es' : 'ca';

    // Set primeNG specific translations
    translate
      .get('SE_COMPONENTS.PRIMENG')
      .pipe(take(1))
      .subscribe((res) => primeNGConfig.setTranslation(res));

    return lastValueFrom(translate.use(currentLang));
  };
}

registerLocaleData(localeCa, 'ca');

@NgModule({
  declarations: [],
  imports: [
    BrowserModule,
    HttpClientModule,
    TranslateModule.forRoot({
      defaultLanguage: 'ca',
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpBackend],
      },
    }),
    YearPeriodModule,
    TaxpayerModule,
    SurchargesModule,
    CalculationsModule,
    SummaryModule,
    SelfAssessmentResultModule,
    TaxFormsModule,
    SeComplementaryModule,
    ConfirmRecoverDraftModule,
    DadesDocumentModule,
    WorkingSessionsModule,
    CalculTreeModule,
    ModifySaveButtonsModule,
    DataFiTerminiComponentModule,
    AutoliquidacionsForaTerminiModule,
    AutoliquidacionsAnteriorsModule,
    AutoPresResultatModule,
    AutoPendentsTramitarModule,
    AutoliquidacionsComplementariaPerduaFiscalModule,
  ],
  providers: [
    { provide: LOCALE_ID, useValue: 'ca-ES' },
    {
      provide: DEFAULT_CURRENCY_CODE,
      useValue: 'EUR',
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: SeHttpInterceptorService,
      multi: true,
    },
    CookieService,
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService, PrimeNGConfig],
      multi: true,
    },
  ],
})
export class AppModule implements DoBootstrap {
  constructor(
    private injector: Injector,
    private loginService: SeLoginService,
  ) {}

  ngDoBootstrap(appRef: ApplicationRef): void {
    if (isDevMode()) {
      this.loginService.login(true).then(
        () => {
          // appRef.bootstrap(TaxpayerComponent, 'app-root');
          appRef.bootstrap(SelfAssessmentResultComponent, 'app-root');
          // appRef.bootstrap(TaxFormFieldComponent, 'app-root');
        },
        (error) => {
          console.error('No se ha podido iniciar sesión.');
          throw error;
        },
      );
    }

    const elements = [
      { tag: 'mf-tributs-taxpayer', component: TaxpayerComponent },
      { tag: 'mf-tributs-year-period', component: YearPeriodComponent },
      { tag: 'mf-tributs-surcharges', component: SurchargesComponent },
      { tag: 'mf-tributs-calculations', component: CalculationsComponent },
      { tag: 'mf-tributs-summary', component: SummaryComponent },
      {
        tag: 'mf-tributs-self-assessment-result',
        component: SelfAssessmentResultComponent,
      },
      {
        tag: 'mf-tributs-autoliquidacio-complementaria',
        component: AutoliquidacioComplementariaComponent,
      },
      {
        tag: 'mf-autoliquidacions-pendents-tramitar',
        component: AutoPendentsTramitarComponent,
      },
      {
        tag: 'mf-autoliquidacions-presentades-info-pagament',
        component: InformacioPagamentComponent,
      },
      {
        tag: 'mf-autoliquidacions-presentades-resultat',
        component: AutoPresResultatComponent,
      },
      { tag: 'mf-tributs-tax-form-group', component: TaxFormGroupComponent },
      {
        tag: 'mf-tributs-confirm-recover-draft',
        component: ConfirmRecoverDraftComponent,
      },
      {
        tag: 'mf-tributs-automatic-complementary',
        component: SeAutomaticComplementaryComponent,
      },
      {
        tag: 'mf-tributs-manual-complementary',
        component: SeManualComplementaryComponent,
      },
      {
        tag: 'mf-tributs-dades-document',
        component: DadesDocumentComponent,
      },
      {
        tag: 'mf-tributs-working-sessions',
        component: WorkingSessionsComponent,
      },
      {
        tag: 'mf-tributs-modify-save-buttons',
        component: ModifySaveButtonsComponent,
      },
      {
        tag: 'mf-tributs-data-fi-termini',
        component: DataFiTerminiComponent,
      },
      {
        tag: 'mf-tributs-autoliquidacions-complementaria-perdua-fiscal',
        component: AutoliquidacionsComplementariaPerduaFiscalComponent,
      },
      {
        tag: 'mf-tributs-autoliquidacions-fora-termini',
        component: AutoliquidacionsForaTerminiComponent,
      },
      {
        tag: 'mf-tributs-calcul-resum',
        component: CalculResumComponent,
      },
      {
        tag: 'mf-tributs-calcul-tree-view',
        component: CalculTreeViewComponent,
      },
      {
        tag: 'mf-tributs-modify-save-buttons',
        component: ModifySaveButtonsComponent,
      },
      {
        tag: 'mf-tributs-autoliquidacions-anteriors',
        component: AutoliquidacionsAnteriorsComponent,
      },
      {
        tag: 'mf-tributs-autoliquidacions-anteriors-old',
        component: AutoliquidacionsAnteriorsOldComponent,
      },
      // ...añade más custom elements aquí...
    ];

    elements.forEach(({ tag, component }) => {
      const ce = createCustomElement(component, { injector: this.injector });
      if (!customElements.get(tag)) customElements.define(tag, ce);
    });
  }
}

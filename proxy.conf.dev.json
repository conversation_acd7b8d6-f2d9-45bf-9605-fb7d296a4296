{"/mf/pt-commons-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/pt-commons-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-commons-mf": ""}}, "/mf/se-documents-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-documents-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-documents-mf": ""}}, "/mf/se-tributs-mf/*": {"target": "http://127.0.0.1:5500/", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-tributs-mf": ""}}, "/mf/se-seguretat-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-seguretat-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-seguretat-mf": ""}}, "/mf/se-presentacions-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-presentacions-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-presentacions-mf": ""}}, "/mf/se-pagaments-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-pagaments-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-pagaments-mf": ""}}, "/api/tributs/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/tributs", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/tributs": ""}}, "/api/dades-referencia/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/dades-referencia", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/dades-referencia": ""}}, "/api/documents/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/documents", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/documents": ""}}, "/api/seguretat-admin/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/seguretat-admin", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat-admin": ""}}, "/api/gasos/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/gasos", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/gasos": ""}}, "/api/seguretat/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/seguretat", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat": ""}}, "/api/pagaments/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/pagaments", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/pagaments": ""}}, "/api/presentacions/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/presentacions", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/presentacions": ""}}, "/api/recurs/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/recurs", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/recurs": ""}}, "/api/secured/pagaments/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/secured/pagaments", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/secured/pagaments": ""}}}
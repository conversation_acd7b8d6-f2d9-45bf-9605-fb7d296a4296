import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Column, Row } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-autoliquidacions-anteriors',
  templateUrl: './autoliquidacions-anteriors.component.html',
  styleUrls: ['./autoliquidacions-anteriors.component.scss'],
})
export class AutoliquidacionsAnteriorsComponent {
  @Input() modelFraccionament: string = '';
  @Input() goingColumns: Column[] = [];
  @Input() goingRows: Row[] = [];
  @Input() autoExpedientMap: Map<string, string[]> = new Map<
    string,
    string[]
  >();

  @Output() newFormEvent = new EventEmitter<boolean>();
  @Output() goToPagamentEvent = new EventEmitter<string[]>();
  @Output() redirectSessionTreballEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  newFormActionHandler(): void {
    this.newFormEvent.emit(true);
  }

  goToPagamentHandler(event: Event): void {
    const idsAutoliquidacio: string[] = (event as CustomEvent<string[]>).detail;

    if (idsAutoliquidacio.length) {
      this.goToPagamentEvent.emit(idsAutoliquidacio);
    }
  }

  redirectSessionTreballHandler(): void {
    this.redirectSessionTreballEvent.emit();
  }
}

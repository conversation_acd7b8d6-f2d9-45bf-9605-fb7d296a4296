import { CommonModule } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TAX_MODEL } from '@core/models';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import {
  DateUtilsService,
  Nullable,
  SeAlertModule,
  SeAlertType,
  SeButtonModule,
  SeDatepickerModule,
  SeFormControlErrorModule,
  SeInputModule,
  SeLinkModule,
  SePanelModule,
  SeRadioModule,
  SeSwitchModule,
  SeValidations,
} from 'se-ui-components-mf-lib';
import { UtilsService } from '../dades-document/services/utils.service';
import { AutoliquidacioComplementariaService } from './autoliquidacio-complementaria.service';
import { ResponseCheckComplementari } from './models/autoliquidacio-complementaria.model';

@Component({
  selector: 'app-autoliquidacio-complementaria',
  templateUrl: './autoliquidacio-complementaria.component.html',
  styles: [],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    SeRadioModule,
    SeLinkModule,
    TranslateModule,
    SeInputModule,
    SeDatepickerModule,
    SePanelModule,
    SeButtonModule,
    SeAlertModule,
    SeSwitchModule,
    SeFormControlErrorModule,
  ],

  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AutoliquidacioComplementariaComponent implements OnDestroy {
  private _numJustificantPattern: string | RegExp = '';

  @Input()
  get numJustificantPattern(): string | RegExp {
    return this._numJustificantPattern;
  }
  set numJustificantPattern(value: string | RegExp) {
    this._numJustificantPattern = value;
    this.setComplementaryValidators(value);
  }

  private _IMPOST: Nullable<TAX_MODEL>;

  @Input()
  get IMPOST(): Nullable<TAX_MODEL> {
    return this._IMPOST;
  }
  set IMPOST(value: Nullable<TAX_MODEL>) {
    this._IMPOST = value;
  }

  @Input() urlHelpComplementaries: Nullable<string>;

  @Output() backEvent: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() redirectSessionEvent: EventEmitter<string> =
    new EventEmitter<string>();

  @Output() formDataOutput: EventEmitter<string> = new EventEmitter<string>();

  SeAlertType = SeAlertType;
  autoCompForm: FormGroup = this.fb.group({
    numJustificant: [],
    quotaLiquida: [],
    dataPresentacio: [],
  });

  today: Date = new Date();

  private readonly unsubscribe: Subject<void> = new Subject();

  constructor(
    private readonly fb: FormBuilder,
    private readonly utilsService: UtilsService,
    private readonly autoliquidacioComplementariaService: AutoliquidacioComplementariaService,
    private readonly dateUtilsService: DateUtilsService,
  ) {
    this.autoliquidacioComplementariaService.redirect$
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((redirect) => {
        this.redirectSessionEvent.emit(redirect);
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  setComplementaryValidators(numJustificantPattern: string | RegExp): void {
    const validators = [
      Validators.required,
      Validators.pattern(numJustificantPattern),
    ];
    this.getFormControl('numJustificant')?.setValidators(validators);
    this.autoCompForm.updateValueAndValidity();
    this.addRequiredValidator('quotaLiquida', [
      Validators.required,
      Validators.min(0),
      Validators.max(100_000_000),
    ]);
    this.addRequiredValidator('dataPresentacio', [
      Validators.required,
      SeValidations.dateRange(
        null,
        this.today,
        '',
        'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.DATA_DARRERA_MAX_ERROR',
      ),
    ]);
  }

  clearAndResetFormControl(name: string, reset = true): void {
    this.utilsService.clearAndResetFormControl(this.autoCompForm, name, reset);
  }

  addRequiredValidator(name: string, validators = [Validators.required]): void {
    this.utilsService.addRequiredValidator(this.autoCompForm, name, validators);
  }

  getFormControl(name: string): FormControl {
    return this.autoCompForm.get(name) as FormControl;
  }

  navigateBackwards(): void {
    this.backEvent.emit(true);
  }

  checkAutoComp(): void {
    this.autoCompForm.markAsDirty();
    this.autoCompForm.markAllAsTouched();

    if (this.autoCompForm.valid) {
      const numJustificant = this.autoCompForm.value['numJustificant'];
      const quotaTotal = this.autoCompForm.value['quotaLiquida'];
      const dataPresentacio = this.dateUtilsService.formatDate(
        this.autoCompForm.value['dataPresentacio'],
        'yyyy-MM-dd',
      );

      if (
        numJustificant &&
        typeof quotaTotal === 'number' &&
        dataPresentacio &&
        this.IMPOST
      ) {
        this.autoliquidacioComplementariaService
          .checkAutoComp({
            numJustificant,
            quotaTotal,
            dataPresentacio,
            tramit: this.IMPOST,
          })
          .subscribe((response: ResponseCheckComplementari | undefined) => {
            if (response && response.existeixComplementaria) {
              this.formDataOutput.emit(this.autoCompForm.getRawValue());
              response.idFormulari &&
                this.redirectSessionEvent.emit(response.idFormulari);
            }
          });
      }
    }
  }
}

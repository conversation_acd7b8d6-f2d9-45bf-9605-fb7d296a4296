import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { Column, Row } from 'se-ui-components-mf-lib';
import { TableSelfassessmentData } from '../../self-assessment-result/self-assessment-result.model';
import { AutoliquidacionsAnteriorsColumns } from './autoliquidacions-anteriors-old.model';

@Component({
  selector: 'app-autoliquidacions-anteriors',
  templateUrl: './autoliquidacions-anteriors-old.component.html',
  styleUrls: ['./autoliquidacions-anteriors-old.component.scss'],
})
export class AutoliquidacionsAnteriorsOldComponent {
  AUTOLIQUIDACIONS_ANTERIORS_BASE_TRANSLATE =
    'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS';

  @Input() panelTitleOnGoing =
    `${this.AUTOLIQUIDACIONS_ANTERIORS_BASE_TRANSLATE}.TITLE_PANEL_ONGOING`;
  @Input() subtitleOnGoing =
    `${this.AUTOLIQUIDACIONS_ANTERIORS_BASE_TRANSLATE}.SUBTITLE_ONGOING`;
  @Input() panelTitle =
    `${this.AUTOLIQUIDACIONS_ANTERIORS_BASE_TRANSLATE}.TITLE_PANEL`;
  @Input() subtitle =
    `${this.AUTOLIQUIDACIONS_ANTERIORS_BASE_TRANSLATE}.SUBTITLE`;

  @Input() goingColumns: Column[] = [];
  @Input() goingRows: Row[] = [];
  @Input() selfassessmentsIds: string[] = [];

  @Output() newAutoliquidacionChange = new EventEmitter();
  @Output() goToPagamentChange = new EventEmitter<TableSelfassessmentData>();

  columns: string[] = [
    AutoliquidacionsAnteriorsColumns.JUSTIFICANT,
    AutoliquidacionsAnteriorsColumns.TAXPAYER,
    AutoliquidacionsAnteriorsColumns.TOTAL,
    AutoliquidacionsAnteriorsColumns.DATE,
    AutoliquidacionsAnteriorsColumns.STATE,
  ];
  @Input() actions: MenuItem[] | undefined;

  newFormAction = (): void => {
    this.newAutoliquidacionChange.emit();
  };

  goToPagament = (event: Event): void => {
    const data: TableSelfassessmentData = (
      event as CustomEvent<{ rowData: TableSelfassessmentData }>
    ).detail.rowData;
    this.goToPagamentChange.emit(data);
  };
}

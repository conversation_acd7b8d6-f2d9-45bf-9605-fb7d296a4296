import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import { SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import {
  RequestComplementaryFormulari,
  ResponseCheckComplementari,
  ResponseCrearSesionComplementaria,
} from './models/autoliquidacio-complementaria.model';
@Injectable({
  providedIn: 'root',
})
export class AutoliquidacioComplementariaEndpointsService {
  constructor(private readonly httpService: SeHttpService) {}

  crearSesioncomplementaria(
    request: RequestComplementaryFormulari,
  ): Observable<SeHttpResponse<ResponseCrearSesionComplementaria>> {
    return this.httpService.post({
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacio/crear-complementaria`,
      method: 'post',
      body: request,
    });
  }

  checkAutoComp(
    request: RequestComplementaryFormulari,
  ): Observable<SeHttpResponse<ResponseCheckComplementari>> {
    return this.httpService.post({
      baseUrl: environment.baseUrlTributs,
      url: '/sessions-treball/existeix-complementaria',
      method: 'post',
      body: request,
    });
  }
}
